 body {
        margin: 0;
        padding: 0;
        height: 100vh;
        background-image: url('https://i.pinimg.com/originals/d7/2b/56/d72b567349c0b0e0b8b37e14ec5e60c7.jpg');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
    }

    .create_page {
        align-items: center;
        font-family: Arial, Helvetica, sans-serif;
        color: white;
        font-size: 16px;
        width: 750px;
        padding: 30px;
        background-color: #1a1817;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        top: 45%;
        left: 50%;
        transform: translate(-50%, -50%);
        position: absolute;
        margin: 40px 0;
    }

    .create_page h1 {
        text-align: center;
    }

    .form_sections {
        display: flex;
        gap: 40px;
        flex-wrap: wrap;
    }

    .credentials,
    .personal_information {
        flex: 1;
        min-width: 300px;
    }

    .fields input {
        width: 100%;
        padding: 10px;
        font-family: Arial, Helvetica, sans-serif;
        font-size: 14px;
        color: white;
        background-color: #2c2b2a;
        border: 1px solid #ccc;
        margin-bottom: 10px;
        box-sizing: border-box;
    }

    .fields input::placeholder {
        color: #ccc;
    }

    .fields button {
        background-color: white;
        color: black;
        font-size: 16px;
        padding: 10px;
        width: 100px;
        font-family: Arial, Helvetica, sans-serif;
        border: none;
        outline: none;
        cursor: pointer;
    }

    .fields a {
        font-family: Arial, Helvetica, sans-serif;
        font-size: 16px;
        margin-left: 10px;
        color: white;
        text-decoration: none;
    }

    .credentials button:hover , .credentials select:hover{
        background-color: lightgray;
    }

    .credentials input:hover, .personal_information input:hover {
        background-color: #3c3b3a;
    }

    .credentials a:hover{
        text-decoration: underline;
    }

    

    